With(
    {
        svcLower: Lower(Trim(Coalesce(drp_Teenus.Selected.Value, ""))),
        mkLower: Lower(Trim(Coalesce(Dropdown1.Selected.Value, "")))
    },
    With(
        {
            selNimi: If(
                Find("raamatupidami", svcLower) > 0 Or Find("revisjoni", svcLower) > 0,
                "<PERSON><PERSON> Saveljev",
                If(
                    Find("koristus", svcLower) > 0,
                    "<PERSON>",
                    If(
                        Find("haldusteenused", svcLower) > 0,
                        "<PERSON>re Kastor",
                        If(
                            Find("tehnohooldus", svcLower) > 0,
                            "<PERSON>var <PERSON>am",
                            <PERSON>lank()
                        )
                    )
                )
            )
        },
        If(
            svcLower = "maaklerteenus" Or Find("maakler", svcLower) > 0,
            Sort(
                Filter(
                    MHtabel,
                    Find("maakler", Lower(Coalesce(Ametinimetus, ""))) > 0 And
                    (IsBlank(mkLower) Or Lower(Trim(Coalesce(Maakond, ""))) = mkLower)
                ),
                "<PERSON><PERSON>", SortOrder.Ascending
            ),
            If(
                svcLower = "hindamisteenus" Or Find("hindam", svcLower) > 0,
                Sort(
                    Filter(
                        MHtabel,
                        (Find("hindaja", Lower(Coalesce(Ametinimetus, ""))) > 0 Or
                         Find("hindam", Lower(Coalesce(Ametinimetus, ""))) > 0) And
                        (IsBlank(mkLower) Or Lower(Trim(Coalesce(Maakond, ""))) = mkLower)
                    ),
                    "Nimi", SortOrder.Ascending
                ),
                If(
                    Not(IsBlank(selNimi)),
                    Filter(
                        MHtabel,
                        Lower(Trim(Coalesce(Nimi, ""))) = Lower(Trim(selNimi))
                    ),
                    MHtabel
                )
            )
        )
    )
)