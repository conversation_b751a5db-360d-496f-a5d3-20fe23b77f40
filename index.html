With(
    {
        // Safely get and normalize input values
        svcLower: Lower(Trim(Coalesce(drp_Teenus.Selected.Value, ""))),
        mkLower: Lower(Trim(Coalesce(Dropdown1.Selected.Value, "")))
    },
    With(
        {
            // Service name mapping for specific services
            selNimi: If(
                Find("raamatupidami", svcLower) > 0 Or Find("revisjoni", svcLower) > 0,
                "<PERSON>lin Saveljev",
                If(
                    Find("koristus", svcLower) > 0,
                    "<PERSON>",
                    If(
                        Find("haldusteenused", svcLower) > 0,
                        "Kaire Kastor",
                        If(
                            Find("tehnohooldus", svcLower) > 0,
                            "Alvar <PERSON>am",
                            Blank()
                        )
                    )
                )
            )
        },
        // Main filtering logic
        If(
            // Check for real estate broker services
            svcLower = "maaklerteenus" Or Find("maakler", svcLower) > 0,
            Sort(
                Filter(
                    MHtabel,
                    Find("maakler", Lower(Coalesce(Ametinimetus, ""))) > 0 And
                    (IsBlank(mkLower) Or Lower(Trim(Coalesce(Maakond, ""))) = mkLower)
                ),
                "Nimi", SortOrder.Ascending
            ),
            If(
                // Check for appraisal services
                svcLower = "hindamisteenus" Or Find("hindam", svcLower) > 0,
                Sort(
                    Filter(
                        MHtabel,
                        (Find("hindaja", Lower(Coalesce(Ametinimetus, ""))) > 0 Or
                         Find("hindam", Lower(Coalesce(Ametinimetus, ""))) > 0) And
                        (IsBlank(mkLower) Or Lower(Trim(Coalesce(Maakond, ""))) = mkLower)
                    ),
                    "Nimi", SortOrder.Ascending
                ),
                // Default case: filter by specific person or return all
                If(
                    Not(IsBlank(selNimi)),
                    Filter(
                        MHtabel,
                        Lower(Trim(Coalesce(Nimi, ""))) = Lower(Trim(selNimi))
                    ),
                    MHtabel
                )
            )
        )
    )
)